"use client"

import React from "react"
import { But<PERSON> } from "@kickavenue/ui/components"

import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { useUserStore } from "stores/userStore"
import Modal from "@components/shared/Modal"
import ModalHeader from "@components/shared/ModalParts/ModalHeader"
import Table from "@components/Table"

import { useDeleteUser } from "../hooks/useDeleteUser"

const columns = [
  {
    key: "username",
    title: "Username",
    dataIndex: "username",
  },
  {
    key: "email",
    title: "Email",
    dataIndex: "email",
  },
  {
    key: "jobTitleName",
    title: "Job Title",
    dataIndex: "jobTitleName",
  },
  {
    key: "status",
    title: "Status",
    dataIndex: "status",
  },
]
const DeleteBulkUserModal: React.FC = () => {
  const { setOpen, modalId, open } = useModalStore()
  const { selectedRowKeys, setSelectedRowKeys, selectedItems } = useUserStore()
  const { handleBulkDeleteUsers, isPending } = useDeleteUser()
  const { DELETE_BULK_CONFIRM } = ModalConstant.MODAL_IDS

  if (!open || modalId !== DELETE_BULK_CONFIRM) return null

  const handleDelete = async () => {
    await handleBulkDeleteUsers(selectedRowKeys)
    setSelectedRowKeys([])
    setOpen(false, DELETE_BULK_CONFIRM)
  }

  const handleClose = () => {
    setOpen(false, DELETE_BULK_CONFIRM)
  }

  return (
    <Modal
      modalId={DELETE_BULK_CONFIRM}
      className="relative sm:!max-w-[564px]"
      onClose={handleClose}
    >
      <ModalHeader title="Delete User" onClose={handleClose} />
      <div className="flex flex-col gap-lg p-lg pb-[150px]">
        <Table columns={columns} dataSource={selectedItems} rowKey="id" />
      </div>
      <div className="absolute bottom-0 flex w-full gap-lg bg-white p-lg shadow-base">
        <Button
          size="lg"
          variant="secondary"
          onClick={handleClose}
          disabled={isPending}
          className="!w-full"
        >
          Cancel
        </Button>
        <Button
          onClick={handleDelete}
          size="lg"
          variant="primary"
          className="!w-full"
          disabled={isPending}
        >
          Confirm
        </Button>
      </div>
    </Modal>
  )
}

export default DeleteBulkUserModal

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { useRouter } from "next/navigation"

import { useShowToast } from "@app/hooks/useShowToast"
import { DeleteUser } from "@application/usecases/deleteUser"
import { UserApiRepository } from "@infrastructure/repositories/userApiRepository"
import { QueryKeys } from "@constants/queryKeys"
import { useModalStore } from "stores/modalStore"
import { useUserStore } from "stores/userStore"

export const useDeleteUser = () => {
  const queryClient = useQueryClient()
  const showToast = useShowToast()
  const [isPending, setIsPending] = useState(false)
  const router = useRouter()
  const { setOpen } = useModalStore()
  const { setSelectedRowKeys, setSelectedItems } = useUserStore()

  const deleteUser = async (id: number) => {
    const repository = new UserApiRepository()
    const usecase = new DeleteUser(repository)
    return usecase.execute(id)
  }

  const { mutate } = useMutation({
    mutationFn: deleteUser,
    onSuccess: () => {
      showToast("User deleted successfully", "success")
      // Invalidate and refetch user queries
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.GET_ALL_USERS],
      })
      setOpen(false)
      setSelectedRowKeys([])
      setSelectedItems([])
      router.refresh()
    },
    onError: () => {
      showToast("Failed to delete user", "danger")
    },
  })

  const handleDeleteUser = async (id: number) => {
    setIsPending(true)
    await mutate(id)
    setIsPending(false)
  }

  const handleBulkDeleteUsers = async (ids: number[]) => {
    setIsPending(true)
    try {
      await Promise.all(ids.map((id) => mutate(id)))
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.GET_ALL_USERS],
      })
      showToast("Users deleted successfully", "success")
      setOpen(false)
      router.refresh()
    } catch (error) {
      showToast("Failed to delete users", "danger")
      throw error
    } finally {
      setIsPending(false)
    }
  }

  return {
    handleDeleteUser,
    handleBulkDeleteUsers,
    isPending,
  }
}

"use client"

import { ReactNode, useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Text } from "@kickavenue/ui/dist/src/components"

import MenuItems from "@components/MenuItems"
import Navbar from "@components/Navbar"
import SpinnerLoading from "@components/shared/SpinnerLoading"

interface LayoutProps {
  children: ReactNode
}

const publicRoutes = [
  "/login",
  "/forgot-password",
  "/forgot-password/otp",
  "/create-password",
]

export default function Layout({ children }: LayoutProps) {
  const navbarOpen = true
  const pathname = usePathname()
  const router = useRouter()
  const { data: session, status } = useSession()

  const userRole = "admin"

  const isPublicRoute = publicRoutes.some(
    (route) => pathname === route || pathname.endsWith(route),
  )

  useEffect(() => {
    if (status === "loading") return
    if (!session && !isPublicRoute) {
      router.replace("/login")
    }
  }, [session, status, isPublicRoute, router])

  if (isPublicRoute) {
    return <>{children}</>
  }

  if (status === "loading") {
    return <SpinnerLoading />
  }

  if (!session) {
    return null
  }

  return (
    <>
      <Navbar />
      <div className="flex">
        <nav
          className={`flex flex-col bg-gray-b-75 transition-all duration-300 ${
            navbarOpen ? "w-64" : "w-16"
          }`}
        >
          <div className="max-h-[calc(100vh-(64px+42px))] flex-1 overflow-y-auto py-base pl-1 pr-xs">
            <MenuItems userRole={userRole} />
          </div>
          <div className="flex items-center justify-center border-t border-white px-1 py-sm opacity-50">
            <Text
              size="sm"
              state="primary"
              type="regular"
              className="!text-white"
            >
              Version 0.1.0
            </Text>
          </div>
        </nav>
        <main className="max-h-[calc(100vh-64px)] flex-1 overflow-y-auto p-4">
          {children}
        </main>
      </div>
    </>
  )
}

import React, { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cx } from "class-variance-authority"
import { IconArrowRightOutline } from "@kickavenue/ui/components/icons"
import Text from "@kickavenue/ui/components/Text"

import { MenuItem } from "./types"
import { menuItems } from "./constants"

interface MenuItemsProps {
  userRole: string
}

interface MenuItemComponentProps {
  item: MenuItem
  pathname: string
  userRole: string
  isSubMenu?: boolean
}

const MenuItemComponent: React.FC<MenuItemComponentProps> = ({
  item,
  pathname,
  userRole,
  isSubMenu = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const filteredChildren = item.children?.filter((child) =>
    child.roles.includes(userRole),
  )
  const hasChildren = filteredChildren && filteredChildren.length > 0
  const IconComponent = item.icon

  const handleToggle = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded)
    }
  }

  const isActive = pathname === item.path
  const isSubMenuActive = isSubMenu && isActive

  const ItemContent = () => (
    <div
      className={cx(
        "flex cursor-pointer items-center gap-2 rounded px-3 py-2 transition-colors",
        !isSubMenu && !isActive && "hover:bg-gray-100",
        !isSubMenu && isActive && "bg-gray-200 text-gray-800",
        isSubMenu && "ml-6",
        isSubMenu && !isActive && "hover:bg-white/20",
        isSubMenuActive && "bg-white text-black",
        isSubMenuActive && "font-bold",
      )}
      onClick={handleToggle}
    >
      {IconComponent && (
        <span className="shrink-0">
          {/* eslint-disable-next-line @shopify/jsx-no-complex-expressions */}
          {typeof IconComponent === "function" ? (
            <IconComponent
              className={cx(
                "h-4 w-4 shrink-0",
                isSubMenuActive ? "!text-black" : "!text-white",
              )}
            />
          ) : (
            IconComponent
          )}
        </span>
      )}
      <Text
        className={cx(
          "flex-1",
          isSubMenuActive ? "!text-black" : "!text-white",
        )}
        size="base"
        type={isActive ? "bold" : isExpanded ? "bold" : "regular"}
        state="primary"
      >
        {item.name}
      </Text>
      {hasChildren && (
        <IconArrowRightOutline
          className={cx(
            "transition-transform",
            isExpanded && "rotate-90",
            isSubMenuActive ? "!text-black" : "!text-white",
          )}
        />
      )}
    </div>
  )

  return (
    <li className="mb-1">
      {item.path  (
        <Link href={item.path} className="block">
          <ItemContent />
        </Link>
      ) : (
        <ItemContent />
      )}
			{item.path ? (
        <Link href={item.path} className="block">
          <ItemContent />
        </Link>
      ) : (
        <ItemContent />
      )}

      {hasChildren && isExpanded && (
        <ul className="mt-1">
          {filteredChildren?.map((child) => (
            <MenuItemComponent
              key={child.name}
              item={child}
              pathname={pathname}
              userRole={userRole}
              isSubMenu
            />
          ))}
        </ul>
      )}
    </li>
  )
}

const MenuItems: React.FC<MenuItemsProps> = ({ userRole }) => {
  const pathname = usePathname()

  const filteredItems = menuItems.filter((item) =>
    item.roles.includes(userRole),
  )

  return (
    <ul className="space-y-1">
      {filteredItems.map((item) => (
        <MenuItemComponent
          key={item.name}
          item={item}
          pathname={pathname}
          userRole={userRole}
        />
      ))}
    </ul>
  )
}

export default MenuItems

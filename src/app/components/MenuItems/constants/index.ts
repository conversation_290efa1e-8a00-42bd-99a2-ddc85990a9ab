import { IconAddOutline } from "@kickavenue/ui/components"

import { PageRouteConstant } from "@constants/pageRoute"

import { MenuItem } from "../types"

export const menuItems: MenuItem[] = [
  {
    name: "Dashboard",
    path: PageRouteConstant.DASHBOARD,
    roles: ["admin", "user"],
    icon: IconAddOutline,
  },
  {
    name: "Customer Management",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      {
        name: "Member",
        path: PageRouteConstant.MEMBER,
        roles: ["admin", "user"],
      },
      {
        name: "API Management",
        path: PageRouteConstant.MANAGEMENT_API,
        roles: ["admin", "user"],
      },
      { name: "FAQ", path: PageRouteConstant.FAQ, roles: ["admin", "user"] },
      {
        name: "Seller Verification",
        path: PageRouteConstant.SELLER_VERIFICATION,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "<PERSON> Data",
    roles: ["admin", "user"],
    icon: IconAddOut<PERSON>,
    children: [
      {
        name: "<PERSON>",
        path: PageRouteConstant.BRAND,
        roles: ["admin", "user"],
      },
      {
        name: "Category",
        path: PageRouteConstant.CATEGORY,
        roles: ["admin", "user"],
      },
      {
        name: "Country",
        path: PageRouteConstant.COUNTRY,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "Product Variant",
    path: PageRouteConstant.PRODUCT_VARIANT,
    roles: ["admin", "user"],
    icon: IconAddOutline,
  },
  {
    name: "Seller Listing",
    path: PageRouteConstant.SELLER_LISTING,
    roles: ["admin", "user"],
    icon: IconAddOutline,
  },
  {
    name: "Inventory",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      {
        name: "Stock Level",
        path: PageRouteConstant.STOCK,
        roles: ["admin", "user"],
      },
      {
        name: "Rack",
        path: PageRouteConstant.RACK,
        roles: ["admin", "user"],
      },
      {
        name: "Size Chart",
        path: PageRouteConstant.SIZE_CHART,
        roles: ["admin", "user"],
      },
      {
        name: "Warehouse",
        path: PageRouteConstant.WAREHOUSE,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "Offer",
    path: PageRouteConstant.OFFER,
    roles: ["admin", "user"],
    icon: IconAddOutline,
  },
  {
    name: "Campaign",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      {
        name: "Promotion",
        path: PageRouteConstant.PROMOTION,
        roles: ["admin", "user"],
      },
      {
        name: "Raffle",
        path: PageRouteConstant.RAFFLE,
        roles: ["admin", "user"],
      },
      {
        name: "Blog & News",
        path: PageRouteConstant.BLOG_AND_NEWS,
        roles: ["admin", "user"],
      },
      {
        name: "Collection",
        path: PageRouteConstant.COLLECTION,
        roles: ["admin", "user"],
      },
      {
        name: "Announcement",
        path: PageRouteConstant.ANNOUNCEMENT,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "Setup",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      // there no ui for this yet
      {
        name: "Shortcut",
        path: PageRouteConstant.SHORTCUT,
        roles: ["admin", "user"],
      },
      {
        name: "Job Title",
        path: PageRouteConstant.JOB_TITLE,
        roles: ["admin", "user"],
      },
      {
        name: "Wizard",
        path: PageRouteConstant.WIZARD,
        roles: ["admin", "user"],
      },
      {
        name: "Fee Setup",
        path: PageRouteConstant.FEE_SETUP,
        roles: ["admin", "user"],
      },
      // there no ui for this yet
      {
        name: "Payment Method",
        path: PageRouteConstant.PAYMENT_METHOD,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "Banner",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      {
        name: "Running Text",
        path: PageRouteConstant.RUNNING_TEXT,
        roles: ["admin", "user"],
      },
      {
        name: "Popup Banner",
        path: PageRouteConstant.POPUP_BANNER,
        roles: ["admin", "user"],
      },
      {
        name: "Slider",
        path: PageRouteConstant.BANNER_SLIDER,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "Order",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      {
        name: "All Orders",
        path: PageRouteConstant.ALL_ORDERS,
        roles: ["admin", "user"],
      },
      {
        name: "Outstanding Orders",
        path: PageRouteConstant.OUTSTANDING_ORDERS,
        roles: ["admin", "user"],
      },
      {
        name: "Quality Control",
        path: PageRouteConstant.QUALITY_CONTROL_ORDERS,
        roles: ["admin", "user"],
      },
      {
        name: "Legit Check",
        path: PageRouteConstant.LEGIT_CHECK_ORDERS,
        roles: ["admin", "user"],
      },
      {
        name: "Follow Up Orders",
        path: PageRouteConstant.FOLLOW_UP_ORDERS,
        roles: ["admin", "user"],
      },
      {
        name: "Pre Delivery",
        path: PageRouteConstant.PRE_DELIVERY_ORDERS,
        roles: ["admin", "user"],
      },
      {
        name: "Delivery",
        path: PageRouteConstant.DELIVERY_ORDERS,
        roles: ["admin", "user"],
      },
      {
        name: "Processed Orders",
        path: PageRouteConstant.PROCESSED_ORDERS,
        roles: ["admin", "user"],
      },
      {
        name: "Failed Orders",
        path: PageRouteConstant.FAILED_ORDERS,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "Consignment",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      {
        name: "All Consignment",
        path: PageRouteConstant.ALL_CONSIGNMENTS,
        roles: ["admin", "user"],
      },
      {
        name: "Outstanding Consignment",
        path: PageRouteConstant.OUTSTANDING_CONSIGNMENTS,
        roles: ["admin", "user"],
      },
      {
        name: "Quality Control",
        path: PageRouteConstant.QUALITY_CONTROL_CONSIGNMENTS,
        roles: ["admin", "user"],
      },
      {
        name: "Legit Check",
        path: PageRouteConstant.LEGIT_CHECK_CONSIGNMENTS,
        roles: ["admin", "user"],
      },
      {
        name: "Rack Assignment",
        path: PageRouteConstant.RACK_ASSIGNMENT_CONSIGNMENTS,
        roles: ["admin", "user"],
      },
      {
        name: "Take Out Pre-Delivery",
        path: PageRouteConstant.TAKE_OUT_PRE_DELIVERY_CONSIGNMENTS,
        roles: ["admin", "user"],
      },
      {
        name: "Take Out Delivery",
        path: PageRouteConstant.TAKE_OUT_DELIVERY_CONSIGNMENTS,
        roles: ["admin", "user"],
      },
      {
        name: "Failed Consignment",
        path: PageRouteConstant.FAILED_CONSIGNMENTS,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "Cashier",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      {
        name: "Store",
        path: PageRouteConstant.CASHIER_STORE,
        roles: ["admin", "user"],
      },
      {
        name: "Marketplace",
        path: PageRouteConstant.CASHIER_MARKETPLACE,
        roles: ["admin", "user"],
      },
      {
        name: "Event",
        path: PageRouteConstant.CASHIER_EVENT,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "Finance",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      {
        name: "All Refunds",
        path: PageRouteConstant.REFUNDS,
        roles: ["admin", "user"],
      },
      {
        name: "Need Refund",
        path: PageRouteConstant.NEED_REFUND,
        roles: ["admin", "user"],
      },
      {
        name: "Disbursements",
        path: PageRouteConstant.DISBURSEMENTS,
        roles: ["admin", "user"],
      },
      {
        name: "Payments",
        path: PageRouteConstant.PAYMENTS,
        roles: ["admin", "user"],
      },
    ],
  },
  {
    name: "User Management",
    roles: ["admin", "user"],
    icon: IconAddOutline,
    children: [
      {
        name: "User List",
        path: PageRouteConstant.USER,
        roles: ["admin", "user"],
      },
      {
        name: "Roles and Permissions",
        path: PageRouteConstant.ROLE_AND_AUTHORITIES,
        roles: ["admin", "user"],
      },
    ],
  },
]

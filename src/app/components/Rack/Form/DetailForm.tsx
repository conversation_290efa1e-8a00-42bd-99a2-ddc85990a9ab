import Input from "@components/shared/Form/Input"
import { Switch } from "@components/shared/Form/Switch"
import Textarea from "@components/shared/Form/Textarea"
import { useFieldStoreHandle } from "@app/hooks/useFieldStoreHandle"
import { TRack } from "types/rack.type"
import { useRackStore } from "stores/rackStore"

import WarehouseLocationField from "./WarehouseLocationField"

export default function DetailForm() {
  const { detail, setDetail } = useRackStore()
  const {
    handleChangeText,
    handleChangeNumber,
    handleToggleBoolean,
    applyRequiredHelperText,
    applyRequiredVariant,
  } = useFieldStoreHandle<TRack>({
    setObject: setDetail,
    object: detail,
  })

  return (
    <div className="flex w-1/2 flex-col gap-lg">
      <Input
        label="Name"
        state="required"
        placeholder="Enter Name"
        name="name"
        value={detail?.name ?? ""}
        onChange={(e) => handleChangeText("name", e.currentTarget.value)}
        variant={applyRequiredVariant("name")}
        helperText={applyRequiredHelperText("name", "Name is required")}
      />

      <Textarea
        label="Description"
        placeholder="Enter Description"
        state="required"
        name="description"
        value={detail?.description ?? ""}
        onChange={(e) => handleChangeText("description", e.currentTarget.value)}
      />

      <WarehouseLocationField />

      <Input
        label="Maximum Capacity"
        state="required"
        placeholder="Enter Maximum Capacity"
        name="maximumCapacity"
        value={detail?.maximumCapacity ?? ""}
        onChange={(e) =>
          handleChangeNumber("maximumCapacity", e.currentTarget.value)
        }
        variant={applyRequiredVariant("maximumCapacity")}
        helperText={applyRequiredHelperText(
          "maximumCapacity",
          "Maximum Capacity is required",
        )}
      />

      <Switch
        state="required"
        label="Active"
        name="isActive"
        checked={detail?.isActive ?? false}
        onChange={() => handleToggleBoolean("isActive")}
      />
    </div>
  )
}

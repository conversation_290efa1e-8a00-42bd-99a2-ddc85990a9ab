import { TItemOption } from "@kickavenue/ui/dist/src/components/ComboBox/ComboBox.type"
import { debounce } from "lodash"
import { useEffect, useMemo, useState } from "react"

import useFetchEligibilityName from "@app/hooks/useFetchEligibilityName"
import useFetchEligibilityOptions from "@app/hooks/useFetchEligibilityOptions"
import { PromotionEligibilityType } from "@constants/promotion"
import { usePromotionStore } from "stores/promotionStore"
import { TVoucher } from "types/voucher.type"

interface UseEligibleComboboxProps {
  eligibilityType: PromotionEligibilityType
  fieldKey: keyof TVoucher
}

export default function useEligibleCombobox(props: UseEligibleComboboxProps) {
  const { voucherDetail: detail } = usePromotionStore()
  const [searchInput, setSearchInput] = useState<string>("")

  const [options, setOptions] = useState<TItemOption[]>([])

  const getIdentifier = () => {
    switch (props.eligibilityType) {
      case PromotionEligibilityType.SubCategory:
        return detail?.eligibleSubCategory?.[0]
      case PromotionEligibilityType.Category:
        return detail?.eligibleCategory?.[0]
      case PromotionEligibilityType.Country:
        return detail?.eligibleCountryId?.[0]
      case PromotionEligibilityType.PaymentMethod:
        return detail?.eligiblePaymentMethod?.[0]
      case PromotionEligibilityType.Brand:
        return detail?.eligibleBrand?.[0]
      default:
        return undefined
    }
  }

  const getFilterIdentifier = () => {
    switch (props.eligibilityType) {
      case PromotionEligibilityType.SubCategory:
        return {
          parentId: detail?.eligibleCategory?.[0],
          id: detail?.eligibleSubCategory?.[0],
        }
      default:
        return getIdentifier()
    }
  }

  const { data: nameData, isLoading: isNameLoading } = useFetchEligibilityName({
    id: getIdentifier(),
    type: props.eligibilityType,
    enabled: true,
  })

  useEffect(() => {
    setOptions([
      {
        label: nameData ?? "",
        value: getIdentifier() ?? "",
      },
    ])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nameData])

  const { data: optionsData } = useFetchEligibilityOptions(
    props.eligibilityType,
    searchInput,
    true,
    getFilterIdentifier(),
  )

  useEffect(() => {
    if (optionsData) {
      setOptions(optionsData)
    }
  }, [optionsData])

  const handleSearchInput = (value: string) => {
    debouncedHandleChangeSearchQuery(value)
  }

  const debouncedHandleChangeSearchQuery = useMemo(
    () =>
      debounce((value: string) => {
        setSearchInput(value)
      }, 500),
    [setSearchInput],
  )

  const getSelected = () => {
    return (
      options?.filter((item) =>
        (detail?.[props.fieldKey] as string[])?.includes(item.value),
      )[0] ?? null
    )
  }

  return {
    options,
    handleSearchInput,
    getSelected,
    isNameLoading,
  }
}

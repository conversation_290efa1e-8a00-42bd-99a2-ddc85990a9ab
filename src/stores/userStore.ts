import { create } from "zustand"

import { User, UserManagementId, UserResponse } from "@domain/entities/User"
import { UserFilterManagement, UserFormData } from "@domain/entities/UserFilter"

interface UserState {
  userData: User[]
  userFilter: UserFilterManagement
  selectedRowKeys: number[]
  totalPages: number
  detail: UserManagementId | null
  formData: UserFormData | null
  content: string
  data: UserResponse | null
  isFilterOpen: boolean
  isFetching: boolean
  isCreateNewData: boolean
  selectedItems: User[]
}

interface UserAction {
  setUserData: (userData: User[]) => void
  setUserFilter: (
    userFilter:
      | UserFilterManagement
      | ((prev: UserFilterManagement) => UserFilterManagement),
  ) => void
  setSelectedRowKeys: (selectedRowKeys: number[]) => void
  setTotalPages: (totalPages: number) => void
  setFormData: (formData: UserFormData) => void
  setDetail: (detail: UserManagementId) => void
  setContent: (content: string) => void
  setData: (data: UserResponse | null) => void
  setIsFilterOpen: (isFilterOpen: boolean) => void
  setIsFetching: (isFetching: boolean) => void
  setIsCreateNewData: (isCreateNewData: boolean) => void
  setSelectedItems: (selectedItems: User[]) => void
}

export const useUserStore = create<UserState & UserAction>((set) => ({
  userData: [],
  userFilter: {
    page: 0,
    pageSize: 10,
    totalPages: 0,
    sort: [],
  },
  detail: null,
  formData: null,
  selectedRowKeys: [],
  totalPages: 0,
  content: "",
  data: null,
  isFilterOpen: false,
  isFetching: false,
  isCreateNewData: false,
  selectedItems: [],
  setUserFilter: (userFilter) =>
    set((state) => ({
      userFilter:
        typeof userFilter === "function"
          ? userFilter(state.userFilter)
          : userFilter,
    })),
  setUserData: (userData) => set(() => ({ userData })),
  setSelectedRowKeys: (selectedRowKeys) => set(() => ({ selectedRowKeys })),
  setTotalPages: (totalPages) => set(() => ({ totalPages })),
  setFormData: (formData) => set(() => ({ formData })),
  setDetail: (detail) => set(() => ({ detail })),
  setContent: (content) => set(() => ({ content })),
  setData: (data) => set(() => ({ data })),
  setIsFilterOpen: (isFilterOpen) => set(() => ({ isFilterOpen })),
  setIsFetching: (isFetching) => set(() => ({ isFetching })),
  setIsCreateNewData: (isCreateNewData) => set(() => ({ isCreateNewData })),
  setSelectedItems: (selectedItems) => set(() => ({ selectedItems })),
}))

/* eslint-disable @typescript-eslint/no-extraneous-class */
export class PageRouteConstant {
  static readonly DASHBOARD = "/dashboard"
  static readonly MEMBER = "/member"
  static readonly MANAGEMENT_API = "/management-api"
  static readonly MANAGEMENT_API_FORM = "/management-api/form"
  static readonly SELLER_VERIFICATION = "/seller-verification"
  static readonly BRAND = "/brand"
  static readonly BRAND_CREATE = "/brand/create"
  static readonly CATEGORY = "/category"
  static readonly CATEGORY_CREATE = "/category/create"
  static readonly COUNTRY = "/country"
  static readonly COUNTRY_CREATE = "/country/create"
  static readonly PRODUCT_VARIANT = "/product-variant"
  static readonly PRODUCT_VARIANT_CREATE = "/product-variant/create"
  static readonly SELLER_LISTING = "/seller-listing"
  static readonly SELLER_LISTING_CREATE = "/seller-listing/create"
  static readonly STOCK = "/stock"
  static readonly RACK = "/rack"
  static readonly SIZE_CHART = "/size-chart"
  static readonly SIZE_CHART_FORM = "/size-chart/form"
  static readonly SIZE_CHART_CREATE = "/size-chart/create"
  static readonly WAREHOUSE = "/warehouse"
  static readonly OFFER = "/offer"
  static readonly PROMOTION = "/promotion"
  static readonly PROMOTION_CREATE = "/promotion/create"
  static readonly PROMOTION_DETAILS = "/promotion/details"
  static readonly RAFFLE = "/raffle"
  static readonly RAFFLE_CREATE = "/raffle/create"
  static readonly BLOG_AND_NEWS = "/blog-and-news"
  static readonly BLOG_AND_NEWS_CREATE = "/blog-and-news/create"
  static readonly COLLECTION = "/collection"
  static readonly COLLECTION_CREATE = "/collection/create"
  static readonly ANNOUNCEMENT = "/announcement"
  static readonly ANNOUNCEMENT_CREATE = "/announcement/create"

  static readonly SHORTCUT = "/shortcut"
  static readonly SHORTCUT_CREATE = "/shortcut/create"

  static readonly JOB_TITLE = "/job-title"
  static readonly JOB_TITLE_CREATE = "/job-title/create"
  static readonly JOB_TITLE_FORM = "/job-title/form"

  static readonly WIZARD = "/wizard"
  static readonly WIZARD_CREATE = "/wizard/create"
  static readonly WIZARD_DETAILS = "/wizard/details"
  static readonly WIZARD_SECTION = "/wizard/section"
  static readonly WIZARD_SECTION_CREATE = "/wizard/section/create"
  static readonly WIZARD_CONTENT = "/wizard/content"
  static readonly WIZARD_CONTENT_CREATE = "/wizard/content/create"

  static readonly FEE_SETUP = "/fee-setup"
  static readonly FEE_SETUP_CREATE = "/fee-setup/create"

  static readonly PAYMENT_METHOD = "/payment-method"
  static readonly PAYMENT_METHOD_CREATE = "/payment-method/create"

  static readonly RUNNING_TEXT = "/running-text"
  static readonly RUNNING_TEXT_CREATE = "/running-text/create"

  static readonly POPUP_BANNER = "/popup-banner"
  static readonly POPUP_BANNER_CREATE = "/popup-banner/create"

  static readonly BANNER_SLIDER = "/banner-slider"
  static readonly BANNER_SLIDER_CREATE = "/banner-slider/create"

  static readonly ROLE_AND_AUTHORITIES_FORM = "/role-and-authorities/form"
  static readonly ROLE_AND_AUTHORITIES = "/role-and-authorities"
  static readonly BLOG_AND_NEWS_FORM = "/blog-and-news/form"
  static readonly SELLER_LISTING_FORM = "/seller-listing/form"
  static readonly FAQ = "/faq"
  static readonly FAQ_FORM = "/faq/form"
  static readonly USER = "/users"
  static readonly USER_CREATE = "/users/create"
  static readonly ORDER_DETAILS = "/order-details"
  static readonly ORDER = "/order"
  static readonly ALL_ORDERS = "/order/all"
  static readonly OUTSTANDING_ORDERS = "/order/outstanding"
  static readonly QUALITY_CONTROL_ORDERS = "/order/quality-control"
  static readonly LEGIT_CHECK_ORDERS = "/order/legit-check"
  static readonly FOLLOW_UP_ORDERS = "/order/follow-up"
  static readonly FAQ_DETAILS = "/faq/details"
  static readonly PRE_DELIVERY_ORDERS = "/order/pre-delivery"
  static readonly DELIVERY_ORDERS = "/order/delivery"
  static readonly PROCESSED_ORDERS = "/order/processed"
  static readonly FAILED_ORDERS = "/order/failed"
  static readonly ALL_CONSIGNMENTS = "/consignment/all"
  static readonly CONSIGNMENT = "/consignment"
  static readonly OUTSTANDING_CONSIGNMENTS = "/consignment/outstanding"
  static readonly QUALITY_CONTROL_CONSIGNMENTS = "/consignment/quality-control"
  static readonly LEGIT_CHECK_CONSIGNMENTS = "/consignment/legit-check"
  static readonly RACK_ASSIGNMENT_CONSIGNMENTS = "/consignment/rack-assignment"
  static readonly TAKE_OUT_PRE_DELIVERY_CONSIGNMENTS =
    "/consignment/take-out-pre-delivery"

  static readonly TAKE_OUT_DELIVERY_CONSIGNMENTS =
    "/consignment/take-out-delivery"

  static readonly FAILED_CONSIGNMENTS = "/consignment/failed"

  static readonly BULK_UPDATE_ORDER = "/order/bulk-update"

  static readonly CASHIER_STORE = "/cashier/store"
  static readonly CASHIER_MARKETPLACE = "/cashier/marketplace"
  static readonly CASHIER_EVENT = "/cashier/event"

  static readonly PAYMENTS = "/payments"
  static readonly REFUNDS = "/refunds"
  static readonly NEED_REFUND = "/need-refund"
  static readonly DISBURSEMENTS = "/disbursements"
}
